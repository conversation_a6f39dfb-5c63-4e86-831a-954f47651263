/** 患者API相关类型定义 */

/** 接口返回的患者原始数据 */
export interface PatientApiData {
  /** 患者ID */
  id: number
  /** 姓名 */
  name: string
  /** 性别 */
  gender: 'Male' | 'Female' | 'UNKNOWN'
  /** 年龄 */
  age: number
  /** 检查项目 */
  examType: string
  /** 检查时间 */
  examTime: string
  /** 科室 */
  department: string
  /** 状态 */
  status: 'PENDING_CHECK' | 'CHECKING' | 'ANALYZING' | 'COMPLETED' | 'CANCELLED'
  /** 备注信息 */
  info: string | null
  /** 创建时间 */
  createTime: string
  /** 更新时间 */
  updateTime: string
}

/** 分页查询参数 */
export interface PatientPageParams {
  /** 页码 */
  pageNumber: number
  /** 每页数量 */
  pageSize: number
  /** 患者姓名 */
  name?: string
  /** 科室 */
  department?: string
  /** 状态 */
  status?: string
  /** 开始时间 */
  startTime?: string
  /** 结束时间 */
  endTime?: string
}

/** 搜索查询参数 */
export interface PatientSearchParams {
  /** 页码 */
  pageNumber: number
  /** 每页数量 */
  pageSize: number
  /** 患者姓名 */
  name?: string
  /** 科室 */
  department?: string
  /** 状态 */
  status?: string
  /** 开始时间 */
  startTime?: string
  /** 结束时间 */
  endTime?: string
}

/** 分页查询响应数据 */
export interface PatientPageResponse {
  /** 患者数据列表 */
  records: PatientApiData[]
  /** 当前页码 */
  pageNumber: number
  /** 每页数量 */
  pageSize: number
  /** 总页数 */
  totalPage: number
  /** 总记录数 */
  totalRow: number
}

/** 搜索查询响应数据 */
export interface PatientSearchResponse {
  /** 患者数据列表 */
  records: PatientApiData[]
  /** 当前页码 */
  pageNumber: number
  /** 每页数量 */
  pageSize: number
  /** 总页数 */
  totalPage: number
  /** 总记录数 */
  totalRow: number
}

/** 患者列表响应数据 */
export interface PatientListResponse {
  /** 患者数据列表 */
  data: PatientApiData[]
}

/** 分页查询API响应 */
export type PatientPageApiResponse = ApiResponseData<PatientPageResponse>

/** 搜索查询API响应 */
export type PatientSearchApiResponse = ApiResponseData<PatientSearchResponse>

/** 列表查询API响应 */
export type PatientListApiResponse = ApiResponseData<PatientListResponse>
